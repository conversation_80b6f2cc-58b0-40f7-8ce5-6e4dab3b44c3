import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
from langchain.tools import Tool

@dataclass
class PatientData:
    """Patient demographic and spirometry data container."""
    age: int = 0
    gender: str = "female"
    bmi: Optional[float] = None
    ethnicity: str = ""
    smoking_status: str = "unknown"

    fev1: Optional[float] = None
    pef: Optional[float] = None
    fvc: Optional[float] = None
    fev1_fvc_ratio: Optional[float] = None
    fev2: Optional[float] = None
    fev3: Optional[float] = None
    fef25_75: Optional[float] = None
    tlc: Optional[float] = None
    rv: Optional[float] = None
    dlco: Optional[float] = None

@dataclass
class AnalysisResult:
    """Structured analysis result container."""
    analysis: List[str]
    current_medical_data: List[str]
    additional_lung_measurements: List[str]
    risk_level: str
    conditions: List[str]
    recommendations: List[str]
    confidence: str
    doctor_summary: str
    device_prompt: str

class LungCapacityAnalyzer:
    """Professional lung capacity analysis system."""
    
    NORMAL_RANGES = {
        "male": {
            "20-30": (3.5, 4.8), "30-40": (3.2, 4.5), "40-50": (2.9, 4.2),
            "50-60": (2.6, 3.9), "60-70": (2.3, 3.6), "70+": (2.0, 3.3)
        },
        "female": {
            "20-30": (2.8, 3.8), "30-40": (2.6, 3.6), "40-50": (2.4, 3.4),
            "50-60": (2.2, 3.2), "60-70": (2.0, 3.0), "70+": (1.8, 2.8)
        }
    }
    
    # Accepted ethnicity categories with their adjustment factors
    ETHNICITY_FACTORS = {
        "african or african-american": 0.87,
        "asian or south-asian": 0.93,
        "caucasian/white": 1.0,
        "hispanic or latino": 0.93,
        "middle eastern": 1.0,
        "others": 1.0
    }

    def __init__(self):
        self.analysis_results = []
        self.current_medical_data = []
        self.additional_lung_measurements = []
        self.risk_factors = []
        self.recommendations = []
        self.respiratory_conditions = []

    def _parse_patient_data(self, data: Dict) -> PatientData:
        """Extract and normalize patient data from input with unit handling."""
        try:
            spirometry = data.get("data", {})
        except Exception as e:
            logging.error(f"Error accessing data: {e}")
            raise ValueError(f"Invalid data format: {e}")

        # Extract demographics
        age = spirometry.get("Age", spirometry.get("age", 0))
        sex = spirometry.get("Sex", spirometry.get("sex", ""))
        gender = "male" if sex.lower() == "male" else "female"

        # Process BMI with units beside values only
        bmi_raw = spirometry.get("BMI", spirometry.get("bmi"))
        bmi = None
        bmi_unit = ""

        if bmi_raw is not None and bmi_raw not in ["Unknown", ""]:
            try:
                if isinstance(bmi_raw, str):
                    # Parse "value unit" format (e.g., "28.5 kg/m²")
                    bmi_raw = bmi_raw.strip()

                    # Handle different encodings and representations of the squared symbol
                    bmi_raw = bmi_raw.replace('m2', 'm²')       # Handle m2 as m²
                    bmi_raw = bmi_raw.replace('m^2', 'm²')      # Handle m^2 as m²

                    # Try to handle UTF-8 encoding issues
                    if '\\xc2\\xb2' in bmi_raw:
                        bmi_raw = bmi_raw.replace('\\xc2\\xb2', '²')

                    parts = bmi_raw.split()
                    if len(parts) == 2:
                        try:
                            bmi = float(parts[0])
                            bmi_unit = parts[1]
                        except ValueError as e:
                            logging.warning(f"Could not parse BMI value '{parts[0]}': {e}")
                    elif len(parts) == 1:
                        # Try to parse just the number if no unit
                        try:
                            bmi = float(parts[0])
                            bmi_unit = "kg/m²"  # Default unit
                        except ValueError as e:
                            logging.warning(f"Could not parse BMI value '{parts[0]}': {e}")
                elif isinstance(bmi_raw, (int, float)):
                    bmi = float(bmi_raw)
                    bmi_unit = "kg/m²"  # Default unit
            except Exception as e:
                logging.error(f"Error processing BMI '{bmi_raw}': {e}")
                # Continue without BMI rather than failing

        # Store BMI unit for display
        self.bmi_unit = bmi_unit

        # Process smoking status with specific categories
        smoking_raw = spirometry.get("Smoking_Status", spirometry.get("smoker", spirometry.get("Smoker", "")))
        if isinstance(smoking_raw, bool):
            smoking_status = "Regular smoker or drinker" if smoking_raw else "Non-Smoker, non-drinker"
        elif isinstance(smoking_raw, str):
            smoking_status = smoking_raw.strip()
            # Validate against allowed categories
            valid_smoking_categories = [
                "Non-Smoker, non-drinker",
                "Occasional smoker or drinker",
                "Regular smoker or drinker",
                "Heavy smoker or drinker"
            ]
            if smoking_status not in valid_smoking_categories:
                # Map common variations to valid categories
                smoking_lower = smoking_status.lower()
                if "non" in smoking_lower or "never" in smoking_lower:
                    smoking_status = "Non-Smoker, non-drinker"
                elif "occasional" in smoking_lower or "light" in smoking_lower:
                    smoking_status = "Occasional smoker or drinker"
                elif "heavy" in smoking_lower:
                    smoking_status = "Heavy smoker or drinker"
                elif "current" in smoking_lower or "regular" in smoking_lower or "yes" in smoking_lower:
                    smoking_status = "Regular smoker or drinker"
                else:
                    smoking_status = "Non-Smoker, non-drinker"  # Default
        else:
            smoking_status = "Non-Smoker, non-drinker"



        # Extract spirometry values with units beside values
        def parse_value_with_unit(param_name):
            """Parse value that may have unit beside it (e.g., '2.3 L' or '72 %')"""
            raw_value = spirometry.get(param_name)

            if raw_value in [None, "Unknown", ""]:
                return None, ""

            # If it's already a number, check for separate unit field (backward compatibility)
            if isinstance(raw_value, (int, float)):
                unit = spirometry.get(f"{param_name}_unit", "")
                return float(raw_value), unit

            # If it's a string, parse value and unit
            if isinstance(raw_value, str):
                raw_value = raw_value.strip()

                # Split by space to separate value and unit
                parts = raw_value.split()
                if len(parts) == 2:
                    try:
                        value = float(parts[0])
                        unit = parts[1].lower()
                        return value, unit
                    except ValueError:
                        pass

                # Try to parse as just a number (no unit)
                try:
                    value = float(raw_value)
                    return value, ""
                except ValueError:
                    return None, ""

            return None, ""

        # Parse all spirometry values with their units
        spirometry_params = ["FEV1", "PEF", "FVC", "FEV1_FVC_ratio", "FEV2", "FEV3", "FEF25_75", "TLC", "RV", "DLCO"]
        parsed_values = {}
        self.units = {}

        for param in spirometry_params:
            value, unit = parse_value_with_unit(param)
            parsed_values[param] = value
            self.units[param] = unit

        return PatientData(
            age=age,
            gender=gender,
            bmi=bmi,
            ethnicity=spirometry.get("Ethnicity", spirometry.get("ethnicity", spirometry.get("Race", ""))),
            smoking_status=smoking_status,

            fev1=parsed_values["FEV1"],
            pef=parsed_values["PEF"],
            fvc=parsed_values["FVC"],
            fev1_fvc_ratio=parsed_values["FEV1_FVC_ratio"],
            fev2=parsed_values["FEV2"],
            fev3=parsed_values["FEV3"],
            fef25_75=parsed_values["FEF25_75"],
            tlc=parsed_values["TLC"],
            rv=parsed_values["RV"],
            dlco=parsed_values["DLCO"]
        )



    def _get_age_group(self, age: int) -> str:
        """Determine age group for normal range lookup."""
        if age >= 70:
            return "70+"
        return f"{(age//10)*10}-{((age//10)+1)*10}" if age > 0 else "40-50"

    def _get_normal_range(self, patient: PatientData) -> Tuple[float, float]:
        """Get normal FEV1 range for patient demographics."""
        gender_key = patient.gender if patient.gender in ["male", "female"] else "female"
        age_group = self._get_age_group(patient.age)
        return self.NORMAL_RANGES.get(gender_key, {}).get(age_group, (2.0, 3.5))

    def _format_value(self, value: float, is_percentage: bool = True, unit: str = "") -> str:
        """Format measurement values with appropriate units."""
        if is_percentage and value <= 200:
            return f"{value:.1f}% of predicted"
        elif unit == "L":
            return f"{value:.2f}L"
        elif unit == "mL":
            return f"{value:.0f} mL"
        else:
            return f"{value:.0f}{unit}"

    def _display_patient_info(self, patient: PatientData) -> None:
        """Generate patient demographic display for current medical data section."""
        self.current_medical_data.extend([
            "📋 **YOUR CURRENT MEDICAL DATA:**"
        ])

        if patient.age > 0:
            self.current_medical_data.append(f"Age: {patient.age} years")

        if patient.gender:
            self.current_medical_data.append(f"Sex: {patient.gender}")

        if patient.bmi and patient.bmi > 0:
            bmi_category = ("underweight" if patient.bmi < 18.5 else
                          "normal weight" if patient.bmi < 25 else
                          "overweight" if patient.bmi < 30 else "obese")
            bmi_unit = getattr(self, 'bmi_unit', 'kg/m²')
            self.current_medical_data.append(f"⚖️ BMI: {patient.bmi:.1f} {bmi_unit} ({bmi_category})")

        # Display ethnicity if provided
        if patient.ethnicity and patient.ethnicity.strip() != "":
            self.current_medical_data.append(f"Ethnicity: {patient.ethnicity}")

        # Display smoking and drinking status
        if patient.smoking_status != "unknown":
            self.current_medical_data.append(f"🚬 Smoking status: {patient.smoking_status}")



    def _display_measurements(self, patient: PatientData) -> None:
        """Display spirometry measurements with proper formatting."""
        self.current_medical_data.extend(["", "📊 **MEASURED VALUES:**"])

        # Use units stored during parsing to determine correct display format
        units = getattr(self, 'units', {})

        measurements = [
            (patient.fev1, "FEV1 (Forced Expiratory Volume in 1 second)",
             "mL" if units.get('FEV1', '').lower() in ['ml', 'milliliters'] else "L"),
            (patient.pef, "PEF (Peak Expiratory Flow)",
             "L/min" if units.get('PEF', '').lower() in ['l/min', 'liters/min'] else "%"),
            (patient.fvc, "FVC (Forced Vital Capacity)",
             "%" if units.get('FVC', '').lower() in ['%', 'percent'] else "L"),
            (patient.fev1_fvc_ratio, "FEV1/FVC Ratio", "ratio"),
            (patient.fev2, "FEV2 (Forced Expiratory Volume in 2 seconds)",
             "%" if units.get('FEV2', '').lower() in ['%', 'percent'] else "L"),
            (patient.fev3, "FEV3 (Forced Expiratory Volume in 3 seconds)",
             "%" if units.get('FEV3', '').lower() in ['%', 'percent'] else "L")
        ]

        for value, name, unit in measurements:
            if value is not None:
                if unit == "ratio":
                    self.current_medical_data.append(f"• {name}: {value:.2f}")
                elif unit in ["mL", "L"]:
                    formatted = f"{value:.0f} mL" if unit == "mL" else f"{value:.2f} L"
                    self.current_medical_data.append(f"• {name}: {formatted}")
                else:
                    formatted = self._format_value(value, unit == "%", unit)
                    self.current_medical_data.append(f"• {name}: {formatted}")

        # Additional parameters will be handled in _analyze_additional_parameters
        # Don't add them to current_medical_data to avoid duplication

        self.analysis_results.extend(["", "🩺 **Analysis:**"])

    def _analyze_fev1(self, patient: PatientData) -> None:
        """Analyze FEV1 measurements and provide clinical interpretation."""
        if not patient.fev1 or patient.fev1 <= 0:
            return

        # Convert mL to L for standardized analysis
        fev1_liters = patient.fev1 / 1000 if patient.fev1 >= 10 else patient.fev1

        # Display value with appropriate units and explanation
        if patient.fev1 >= 10:
            self.analysis_results.append(f"🫁 **Your FEV1 (lung power test):** {patient.fev1:.0f} mL ({fev1_liters:.2f} L)")
        else:
            self.analysis_results.append(f"🫁 **Your FEV1 (lung power test):** {patient.fev1:.2f} L")

        self.analysis_results.append("   This measures how much air you can blow out in one second - think of it as your lung's horsepower!")

        # Get normal range and assess severity with explanations
        normal_range = self._get_normal_range(patient)

        if fev1_liters > normal_range[1] * 1.5:
            self.analysis_results.append("🚨 **Abnormally High FEV1:** Your FEV1 is significantly higher than expected.")
            self.analysis_results.append("   This could indicate measurement error, equipment malfunction, or requires medical evaluation.")
            self.risk_factors.append("Abnormally High FEV1")
        elif fev1_liters > normal_range[1] * 1.2:
            self.analysis_results.append("⚠️ **Higher than expected:** Your FEV1 is above the normal range.")
            self.analysis_results.append("   This might be due to measurement variation or could indicate hyperinflation.")
            self.risk_factors.append("Elevated FEV1")
        elif fev1_liters > normal_range[1]:
            self.analysis_results.append("✅ **Excellent!** Your FEV1 is above average - your lungs are performing really well!")
            self.analysis_results.append("   This is actually good news and shows strong respiratory function.")
        elif fev1_liters >= normal_range[0]:
            self.analysis_results.append("✅ **Great news!** Your FEV1 is right where I'd expect it to be for someone your age and gender.")
            self.analysis_results.append("   Your airways are nice and open, and your breathing muscles are working beautifully.")
        elif fev1_liters >= normal_range[0] * 0.8:
            self.analysis_results.append("⚠️ **Slight concern:** Your FEV1 is a bit lower than ideal, but not dramatically so.")
            self.analysis_results.append("   This could be from a minor respiratory issue or just natural variation.")
        elif fev1_liters >= normal_range[0] * 0.6:
            self.analysis_results.append("🟡 **Moderate reduction:** Your FEV1 shows your airways aren't working as efficiently as they should.")
            self.analysis_results.append("   This suggests there might be some obstruction or inflammation affecting your breathing.")
        elif fev1_liters >= normal_range[0] * 0.5:
            self.analysis_results.append("🟠 **Significant concern:** Your FEV1 is quite a bit lower than what I'd like to see.")
            self.analysis_results.append("   Your lungs are having to work harder than they should to move air in and out.")
        else:
            self.analysis_results.append("🔴 **This needs attention:** Your FEV1 shows substantial reduction in lung function.")
            self.analysis_results.append("   Your airways are significantly restricted, which is affecting your breathing capacity.")

        self.analysis_results.append(
            f"📊 For comparison, someone your age ({patient.age}) and gender typically has an FEV1 between "
            f"{normal_range[0]:.1f}-{normal_range[1]:.1f} L"
        )

        # Add risk factors based on FEV1 severity
        if fev1_liters < normal_range[0] * 0.5:
            self.risk_factors.append("Severely Reduced FEV1")
        elif fev1_liters < normal_range[0] * 0.6:
            self.risk_factors.append("Significantly Reduced FEV1")
        elif fev1_liters < normal_range[0] * 0.8:
            self.risk_factors.append("Moderately Reduced FEV1")

    def _analyze_fev2(self, patient: PatientData) -> None:
        """Analyze FEV2 measurements."""
        if not patient.fev2 or patient.fev2 <= 0:
            return

        # Determine if it's percentage or absolute based on units
        units = getattr(self, 'units', {})
        is_percentage = units.get('FEV2', '').lower() in ['%', 'percent']

        if is_percentage:  # Percentage
            self.analysis_results.append(f"🫁 **Your FEV2:** {patient.fev2}% of predicted")
            self.analysis_results.append("   This measures the air you can blow out in 2 seconds - it shows how your airways perform over a longer period.")

            # Add risk factors for percentage FEV2 values
            if patient.fev2 < 50:
                self.analysis_results.append("   🚨 **Severely reduced:** This suggests significant airway obstruction that needs immediate attention.")
                self.risk_factors.append("Severely Reduced FEV2")
            elif patient.fev2 < 70:
                self.analysis_results.append("   ⚠️ **Moderately reduced:** Your airways are showing some obstruction over time.")
                self.risk_factors.append("Moderately Reduced FEV2")
            elif patient.fev2 < 80:
                self.analysis_results.append("   🟡 **Mildly reduced:** There's some minor airway limitation developing.")
                self.risk_factors.append("Mildly Reduced FEV2")
            else:
                self.analysis_results.append("   ✅ **Good performance:** Your airways maintain good flow over 2 seconds.")
        else:  # Absolute value
            self.analysis_results.append(f"🫁 **Your FEV2:** {patient.fev2:.2f}L (measured directly)")
            self.analysis_results.append("   This measures the total air you can blow out in 2 seconds.")

            # Add risk factors for absolute FEV2 values
            # Normal FEV2 is typically higher than FEV1
            if patient.fev2 < 2.5:
                self.risk_factors.append("Reduced FEV2")
            elif patient.fev2 > 6.0:
                self.risk_factors.append("Elevated FEV2")

    def _analyze_fev3(self, patient: PatientData) -> None:
        """Analyze FEV3 measurements."""
        if not patient.fev3 or patient.fev3 <= 0:
            return

        # Determine if it's percentage or absolute based on units
        units = getattr(self, 'units', {})
        is_percentage = units.get('FEV3', '').lower() in ['%', 'percent']

        if is_percentage:  # Percentage
            self.analysis_results.append(f"🫁 **Your FEV3:** {patient.fev3}% of predicted")
            self.analysis_results.append("   This measures the air you can blow out in 3 seconds - it shows your overall airway capacity.")

            # Add risk factors for percentage FEV3 values
            if patient.fev3 < 50:
                self.analysis_results.append("   🚨 **Severely reduced:** This indicates significant respiratory impairment.")
                self.risk_factors.append("Severely Reduced FEV3")
            elif patient.fev3 < 70:
                self.analysis_results.append("   ⚠️ **Moderately reduced:** Your overall airway capacity is compromised.")
                self.risk_factors.append("Moderately Reduced FEV3")
            elif patient.fev3 < 80:
                self.analysis_results.append("   🟡 **Mildly reduced:** There's some limitation in your overall breathing capacity.")
                self.risk_factors.append("Mildly Reduced FEV3")
            else:
                self.analysis_results.append("   ✅ **Excellent capacity:** Your airways maintain strong flow over 3 seconds.")
        else:  # Absolute value
            self.analysis_results.append(f"🫁 **Your FEV3:** {patient.fev3:.2f}L (measured directly)")
            self.analysis_results.append("   This measures the total air you can blow out in 3 seconds.")

            # Add risk factors for absolute FEV3 values
            # Normal FEV3 is typically close to FVC
            if patient.fev3 < 3.0:
                self.risk_factors.append("Reduced FEV3")
            elif patient.fev3 > 7.0:
                self.risk_factors.append("Elevated FEV3")

    def _analyze_fvc(self, patient: PatientData) -> None:
        """Analyze FVC measurements."""
        if not patient.fvc or patient.fvc <= 0:
            return

        # Determine if it's percentage or absolute based on units
        fvc_unit = getattr(self, 'units', {}).get('FVC', '').lower()
        is_percentage = fvc_unit in ['%', 'percent']

        if is_percentage:  # Percentage
            self.analysis_results.append(f"🫁 **Your FVC (total lung capacity):** {patient.fvc}% of what we'd expect")
            self.analysis_results.append("   This measures the total amount of air you can blow out after taking the deepest breath possible.")

            # Add risk factors for percentage FVC values
            if patient.fvc < 50:
                self.risk_factors.append("Severely Reduced FVC")
            elif patient.fvc < 70:
                self.risk_factors.append("Moderately Reduced FVC")

            if patient.fvc >= 80:
                self.analysis_results.append("✅ **Excellent!** Your total lung capacity is right on target.")
                self.analysis_results.append("   Your lungs can expand fully and you're getting good air exchange.")
            elif patient.fvc >= 70:
                self.analysis_results.append("⚠️ **Slightly reduced:** Your lung capacity is a bit lower than ideal.")
                self.analysis_results.append("   This might suggest some mild restriction in how much your lungs can expand.")
            else:
                self.analysis_results.append("🟡 **Concerning reduction:** Your lung capacity is significantly below what I'd like to see.")
                self.analysis_results.append("   This suggests your lungs may not be able to expand fully, which could affect your breathing.")
        else:  # Absolute value
            self.analysis_results.append(f"🫁 **Your FVC:** {patient.fvc:.2f}L (measured directly)")

            # Add risk factors for absolute FVC values
            # Normal FVC for adults is typically 3.0-5.0L depending on age/gender
            if patient.fvc < 2.0:
                self.risk_factors.append("Severely Reduced FVC")
            elif patient.fvc < 2.5:
                self.risk_factors.append("Significantly Reduced FVC")
            elif patient.fvc < 3.0:
                self.risk_factors.append("Moderately Reduced FVC")

    def _analyze_pef(self, patient: PatientData) -> None:
        """Analyze Peak Expiratory Flow measurements."""
        if not patient.pef or patient.pef <= 0:
            return

        if patient.pef <= 200:  # Percentage
            self.analysis_results.append(f"💨 **Your Peak Flow:** {patient.pef}% of predicted")
            self.analysis_results.append("   This is like testing your lung's 'sprint speed' - how fast you can blow air out.")

            if patient.pef >= 80:
                self.analysis_results.append("✅ **Great!** Your airways can open wide when you need them to.")
                self.analysis_results.append("   No signs of significant blockage or narrowing in your breathing passages.")
            else:
                self.analysis_results.append("⚠️ **Reduced flow:** Your airways aren't opening as wide as they should.")
                self.analysis_results.append("   This could mean there's some narrowing or obstruction affecting your breathing.")

                # Add context based on severity and risk factors
                if patient.pef < 50:
                    self.analysis_results.append("   This level of reduction is quite significant and needs attention.")
                    self.risk_factors.append("Severely Reduced PEF")
                elif patient.pef < 60:
                    self.analysis_results.append("   This level of reduction is quite significant and needs attention.")
                    self.risk_factors.append("Significantly Reduced PEF")
                elif patient.pef < 70:
                    self.analysis_results.append("   This is a moderate reduction that's worth investigating.")
                    self.risk_factors.append("Moderately Reduced PEF")
        else:  # Absolute value
            self.analysis_results.append(f"💨 **Your Peak Flow:** {patient.pef:.0f}L/min (measured directly)")

            # Add risk factors for absolute PEF values
            # Normal PEF varies widely but typically 400-600 L/min for adults
            if patient.pef < 250:
                self.risk_factors.append("Severely Reduced PEF")
            elif patient.pef < 350:
                self.risk_factors.append("Moderately Reduced PEF")

    def _add_demographic_insights(self, patient: PatientData) -> None:
        """Add demographic and lifestyle insights."""
        # Personal context
        self.analysis_results.append("")
        self.analysis_results.append("👤 **About you and your results:**")

        # Age and gender context
        if patient.age > 0:
            if patient.age < 30:
                age_context = "You're young, so I expect your lungs to be performing at their peak!"
            elif patient.age < 50:
                age_context = "At your age, your lungs should still be working really well."
            elif patient.age < 65:
                age_context = "As we get older, some gradual decline in lung function is normal, but we want to keep it minimal."
            else:
                age_context = "At your age, maintaining good lung function becomes even more important for your overall health."

            self.analysis_results.append(f"   🎂 You're a {patient.age}-year-old {patient.gender}. {age_context}")

        # BMI context with health implications
        if patient.bmi and patient.bmi > 0:
            bmi_category = ("underweight" if patient.bmi < 18.5 else
                          "normal weight" if patient.bmi < 25 else
                          "overweight" if patient.bmi < 30 else "obese")

            bmi_unit = getattr(self, 'bmi_unit', 'kg/m²')
            self.analysis_results.append(f"   ⚖️ Your BMI is {patient.bmi:.1f} {bmi_unit}, which puts you in the {bmi_category} category.")

            if patient.bmi >= 30:
                self.analysis_results.append("     Being in the obese range can make it harder for your lungs to expand fully and work efficiently.")
            elif patient.bmi >= 25:
                self.analysis_results.append("     Being a bit overweight can sometimes affect breathing, but it's usually not a major factor.")
            elif patient.bmi < 18.5:
                self.analysis_results.append("     Being underweight might indicate muscle weakness that could affect your breathing strength.")
            else:
                self.analysis_results.append("     This is a healthy weight range that supports good lung function!")

        # Smoking status with personal impact
        if patient.smoking_status != "unknown":
            if patient.smoking_status == "Heavy smoker or drinker":
                self.analysis_results.append("   � **About your heavy smoking/drinking:** This is significantly impacting your lung health and overall respiratory function.")
                self.analysis_results.append("     Heavy smoking causes severe damage to lung tissue and airways, while heavy drinking can impair respiratory muscle function.")
            elif patient.smoking_status == "Regular smoker or drinker":
                self.analysis_results.append("   � **About your regular smoking/drinking:** This is likely affecting your lung function and respiratory health.")
                self.analysis_results.append("     Regular smoking damages airways and reduces lung capacity, while regular drinking can affect breathing patterns.")
            elif patient.smoking_status == "Occasional smoker or drinker":
                self.analysis_results.append("   ⚠️ **About occasional smoking/drinking:** Even occasional use can impact lung health over time.")
                self.analysis_results.append("     There's no completely safe level of smoking, and alcohol can affect respiratory function.")
            elif patient.smoking_status == "Non-Smoker, non-drinker":
                self.analysis_results.append("   ✅ **Non-smoker, non-drinker:** This is excellent for your lung health! Your lifestyle supports optimal respiratory function.")
            else:
                # Fallback for any other smoking status values
                self.analysis_results.append("   ℹ️ **About your lifestyle:** Your smoking/drinking habits may affect your lung health.")



        # Ethnicity context using exact user input (if provided)
        if patient.ethnicity and patient.ethnicity.strip() != "":
            ethnicity_lower = patient.ethnicity.lower()
            ethnicity_factor = self.ETHNICITY_FACTORS.get(ethnicity_lower, 1.0)

            self.analysis_results.append(f"   🧬 **Ethnicity consideration:** Based on your {patient.ethnicity} background, I've adjusted the normal ranges accordingly.")

            if ethnicity_factor != 1.0:
                if ethnicity_factor < 1.0:
                    self.analysis_results.append(f"     People of {patient.ethnicity} background typically have slightly lower baseline values, which is completely normal (adjustment factor: {ethnicity_factor}).")
                else:
                    self.analysis_results.append(f"     People of {patient.ethnicity} background typically have slightly higher baseline values, which is completely normal (adjustment factor: {ethnicity_factor}).")
            else:
                self.analysis_results.append(f"     Your {patient.ethnicity} background serves as the reference standard for lung function measurements.")

    def _analyze_additional_parameters(self, patient: PatientData) -> None:
        """Analyze additional lung capacity parameters (TLC, RV, DLCO)."""
        has_additional = any([patient.tlc is not None, patient.rv is not None, patient.dlco is not None])

        if has_additional:
            self.additional_lung_measurements.append("🔬 **ADDITIONAL LUNG MEASUREMENTS:**")

        if patient.tlc is not None:
            self.additional_lung_measurements.append(f"   📏 **Total Lung Capacity (TLC):** {patient.tlc}% of expected")
            self.additional_lung_measurements.append("     This measures the maximum amount of air your lungs can hold.")

            if patient.tlc < 80:
                self.additional_lung_measurements.append("     🟡 **Reduced capacity:** Your lungs can't expand to their full potential.")
                self.additional_lung_measurements.append("     This might suggest scarring or stiffness in your lung tissue.")
                self.risk_factors.append("Reduced TLC")
            else:
                self.additional_lung_measurements.append("     ✅ **Good news:** Your lungs can expand to their full capacity!")

        if patient.rv is not None:
            self.additional_lung_measurements.append(f"   💨 **Residual Volume (RV):** {patient.rv}% of expected")
            self.additional_lung_measurements.append("     This is the air that stays trapped in your lungs even after you breathe out completely.")

            if patient.rv > 120:
                self.additional_lung_measurements.append("     ⚠️ **Air trapping:** More air than normal is getting stuck in your lungs.")
                self.additional_lung_measurements.append("     This often happens when airways are narrowed or blocked.")
                self.risk_factors.append("Elevated RV")
            elif patient.rv < 75:
                self.additional_lung_measurements.append("     🟡 **Lower than expected:** This could indicate restrictive lung disease.")
                self.risk_factors.append("Reduced RV")
            else:
                self.additional_lung_measurements.append("     ✅ **Normal:** The right amount of air stays in your lungs after breathing out.")

        if patient.dlco is not None:
            self.additional_lung_measurements.append(f"   🔄 **Gas Exchange (DLCO):** {patient.dlco}% of expected")
            self.additional_lung_measurements.append("     This measures how well oxygen moves from your lungs into your bloodstream.")

            if patient.dlco < 80:
                self.additional_lung_measurements.append("     🟡 **Reduced efficiency:** Your lungs aren't transferring oxygen as well as they should.")
                self.additional_lung_measurements.append("     This could mean the tiny air sacs in your lungs are damaged or thickened.")
                self.risk_factors.append("Reduced DLCO")
            else:
                self.additional_lung_measurements.append("     ✅ **Excellent:** Oxygen is moving efficiently from your lungs to your blood!")

    def _get_missing_parameters(self, patient: PatientData) -> List[str]:
        """Identify missing critical parameters."""
        missing = []
        critical_params = [
            ("FEV1", patient.fev1),
            ("PEF", patient.pef)
        ]

        for param_name, param_value in critical_params:
            if param_value is None:
                missing.append(param_name)

        return missing

    def _determine_risk_level(self) -> str:
        """Determine overall respiratory risk level."""
        # Check for severe risk factors that automatically trigger High risk
        # Note: Elevated values are not considered high risk unless abnormally high
        severe_factors = [
            "Severely Reduced FEV1", "Severely Reduced FVC", "Severely Reduced PEF",
            "Severely Reduced DLCO", "Low FEV1/FVC ratio", "Abnormally High FEV1"
        ]

        if any(factor in self.risk_factors for factor in severe_factors):
            return "High"
        elif len(self.risk_factors) >= 3:
            return "High"
        elif len(self.risk_factors) >= 1:
            return "Moderate"
        return "Low"

    def _identify_respiratory_conditions(self, patient: PatientData) -> None:
        """Identify potential respiratory conditions based on test patterns."""

        # Check for abnormally high values (measurement errors)
        if any("Abnormally High" in factor for factor in self.risk_factors):
            self.respiratory_conditions.append("Possible measurement error or equipment malfunction")
            return

        # Check for COPD pattern (low FEV1/FVC ratio + reduced flows)
        if patient.fev1_fvc_ratio and patient.fev1_fvc_ratio < 0.70:
            if any("Reduced FEV1" in factor for factor in self.risk_factors):
                if "current" in patient.smoking_status.lower():
                    self.respiratory_conditions.append("Possible COPD (smoking-related)")
                else:
                    self.respiratory_conditions.append("Possible COPD")

                # Check for emphysema pattern (high TLC + low DLCO)
                if patient.tlc and patient.tlc > 110 and patient.dlco and patient.dlco < 60:
                    self.respiratory_conditions.append("Possible emphysema")

        # Check for restrictive pattern (normal/high FEV1/FVC ratio + reduced volumes)
        elif patient.fev1_fvc_ratio and patient.fev1_fvc_ratio >= 0.70:
            if (patient.tlc and patient.tlc < 80) or any("Reduced FVC" in factor for factor in self.risk_factors):
                self.respiratory_conditions.append("Possible restrictive lung disease")

                # Check for pulmonary fibrosis pattern (low DLCO + restrictive pattern)
                if patient.dlco and patient.dlco < 60:
                    self.respiratory_conditions.append("Possible pulmonary fibrosis")

        # Check for asthma pattern (variable obstruction, often reversible)
        if patient.pef and patient.pef < 80 and patient.fev1_fvc_ratio and patient.fev1_fvc_ratio < 0.75:
            if "non-smoker" in patient.smoking_status.lower():
                self.respiratory_conditions.append("Possible asthma")

        # If no specific patterns identified but risk factors present
        if not self.respiratory_conditions and self.risk_factors:
            self.respiratory_conditions.append("Respiratory function abnormalities requiring evaluation")

    def _assess_confidence(self, patient: PatientData, missing_params: List[str]) -> str:
        """Assess confidence level based on available parameters."""
        core_params_count = sum([
            patient.age > 0,
            patient.gender != "",
            patient.bmi is not None,
            patient.ethnicity != "",
            patient.smoking_status != "unknown",
            patient.fev1 is not None,
            patient.pef is not None
        ])

        if core_params_count >= 6:
            return "High"
        elif core_params_count >= 4:
            return "Moderate"
        return "Low"

    def _generate_doctor_summary(self, patient: PatientData, risk_level: str, confidence: str, user_id: str = "there") -> str:
        """Generate comprehensive doctor-like summary that starts the output."""
        # Start with a personalized greeting using user_id
        summary = f"Hello {user_id}, I've completed a comprehensive analysis of your lung function tests and would like to walk you through your results. "

        # Add key measurements with context
        key_results = []
        if patient.fev1:
            if patient.fev1 >= 10:
                fev1_display = f"{patient.fev1:.0f} mL ({patient.fev1/1000:.2f} L)"
            else:
                fev1_display = f"{patient.fev1:.2f} L"
            key_results.append(f"your FEV1 (forced expiratory volume in 1 second) measures {fev1_display}")

        if patient.pef:
            pef_display = f"{patient.pef:.0f}% of predicted normal values" if patient.pef <= 200 else f"{patient.pef:.0f} L/min"
            key_results.append(f"your peak expiratory flow is {pef_display}")

        if key_results:
            summary += f"Based on your spirometry results, {', and '.join(key_results)}. "

        # Risk-based interpretation with medical context
        if risk_level == "Low":
            summary += "I'm pleased to report that your lung function is within normal parameters and performing optimally. "
            if patient.fev1:
                normal_range = self._get_normal_range(patient)
                fev1_liters = patient.fev1 / 1000 if patient.fev1 >= 10 else patient.fev1
                if fev1_liters >= normal_range[0]:
                    summary += "Your airways demonstrate excellent patency, and your respiratory muscles are functioning at expected capacity. "

            # Add age-appropriate context
            if patient.age > 0:
                if patient.age < 40:
                    summary += f"For a {patient.age}-year-old individual, these results indicate healthy respiratory function with no signs of obstruction or restriction. "
                elif patient.age >= 65:
                    summary += f"At age {patient.age}, maintaining this level of pulmonary function is excellent and suggests good respiratory health maintenance. "
                else:
                    summary += f"These values are well within the expected range for someone of your age ({patient.age}) and demographic profile. "

        elif risk_level == "Moderate":
            summary += "Your test results show some mild abnormalities that warrant monitoring and potential intervention. "
            summary += "While these findings don't indicate immediate respiratory compromise, they suggest areas where we can optimize your lung health. "

            # Add encouraging context
            if patient.smoking_status == "Non-Smoker, non-drinker":
                summary += "The positive aspect is that early detection allows us to implement preventive measures and lifestyle modifications that can significantly improve your respiratory function. "

        else:
            summary += "Your pulmonary function tests reveal significant abnormalities that require prompt medical evaluation and intervention. "
            summary += "While I understand these results may be concerning, early identification and appropriate treatment can substantially improve your respiratory health and quality of life. "

        # Add condition context with medical explanation
        if self.respiratory_conditions and self.respiratory_conditions != ["No specific respiratory conditions identified"]:
            conditions_text = ', '.join(self.respiratory_conditions).lower()
            summary += f"The spirometric pattern suggests possible {conditions_text}. "

            # Add medical context for common conditions
            if "asthma" in conditions_text:
                summary += "Asthma is a highly treatable condition with excellent management options available through bronchodilators and anti-inflammatory medications. "
            elif "copd" in conditions_text:
                summary += "COPD requires comprehensive management, but with proper treatment including bronchodilators, pulmonary rehabilitation, and lifestyle modifications, symptoms can be significantly improved. "

        # Add lifestyle factor analysis
        if patient.smoking_status == "Heavy smoker or drinker":
            summary += "Your current smoking and drinking habits represent the most significant modifiable risk factors affecting your respiratory health. Cessation would provide greater benefit than any pharmacological intervention. "
        elif patient.smoking_status == "Regular smoker or drinker":
            summary += "Your smoking and drinking habits are contributing to respiratory function decline. Cessation or significant reduction would substantially improve your pulmonary health trajectory. "
        elif patient.smoking_status == "Occasional smoker or drinker":
            summary += "Even intermittent tobacco and alcohol use can impact respiratory function. Complete cessation would optimize your lung health potential. "
        elif patient.smoking_status == "Non-Smoker, non-drinker":
            summary += "Your non-smoking, non-drinking lifestyle is an excellent protective factor for maintaining optimal respiratory health. "

        # Clinical confidence assessment
        if confidence == "High":
            summary += "This assessment is based on comprehensive spirometric data providing high diagnostic confidence. "
        elif confidence == "Moderate":
            summary += "This assessment is based on key pulmonary function parameters providing reasonable diagnostic confidence. "
        else:
            summary += "Additional pulmonary function testing would enhance diagnostic accuracy, though current data provides a solid clinical foundation. "

        # Professional transition to detailed analysis
        summary += f"\n\nThe following sections provide detailed spirometric analysis, demographic considerations, and evidence-based recommendations. "

        if risk_level == "High":
            summary += "Given the significant findings, I strongly recommend prompt consultation with a pulmonologist for comprehensive evaluation and treatment planning. "
        elif risk_level == "Moderate":
            summary += "I recommend following the outlined monitoring schedule and implementing the suggested interventions to optimize your respiratory health. "
        else:
            summary += "Continue with the preventive measures outlined below to maintain your excellent pulmonary function. "

        summary += "Please discuss these results with your healthcare provider for personalized medical guidance and follow-up planning."

        return summary

    def _generate_recommendations(self, patient: PatientData, risk_level: str) -> None:
        """Generate personalized recommendations."""
        self.recommendations.extend([
            "🩺 **Clinical Recommendations Based on Your Pulmonary Function Assessment:**",
            ""
        ])

        if risk_level == "Low":
            self.recommendations.extend([
                "✅ **Maintenance of Optimal Respiratory Health:**",
                f"   🏃‍♀️ **Exercise Prescription**: Maintain regular aerobic exercise (150 minutes moderate-intensity weekly) including activities that promote respiratory fitness such as brisk walking, swimming, cycling, or jogging.",
                f"   � **Monitoring Schedule**: Annual spirometry testing to establish baseline trends and detect any early changes in pulmonary function.",
                f"   🌬️ **Environmental Protection**: Minimize exposure to respiratory irritants including volatile organic compounds, particulate matter, occupational dusts, and secondhand smoke.",
                f"   💉 **Preventive Care**: Maintain current vaccinations including annual influenza and pneumococcal vaccines as recommended by CDC guidelines.",
                f"   🫁 **Respiratory Hygiene**: Practice proper breathing techniques and consider respiratory muscle training if engaging in high-intensity activities."
            ])

            # Add age-specific clinical advice
            if patient.age >= 50:
                self.recommendations.append(f"   �‍⚕️ **Age-Specific Guidance**: At {patient.age}, your excellent pulmonary function indicates successful respiratory health maintenance. Continue current lifestyle practices.")
            elif patient.age < 35:
                self.recommendations.append(f"   �‍⚕️ **Preventive Focus**: At {patient.age} with optimal lung function, this is an ideal time to establish lifelong respiratory health habits and avoid risk factors.")

        elif risk_level == "Moderate":
            self.recommendations.extend([
                "� **Let's keep a closer eye on things:**",
                "   📋 I'd like you to get a more complete lung function test that includes something called FVC. It'll give us a much clearer picture of what's going on." if not patient.fvc else "   📋 Your current lung function tests show some areas we should monitor closely. Let's make sure we're tracking any changes.",
                f"   📆 Let's check in again in about 6 months instead of waiting a full year. I want to see if these numbers are staying stable or changing.",
                f"   🚨 If you start noticing any new breathing symptoms - like getting winded easier, coughing more, or feeling tight in your chest - don't wait for your next appointment. Give your doctor a call.",
                f"   💪 This is actually a great time to focus on lung-healthy habits. Small changes now can make a big difference."
            ])
        else:
            self.recommendations.extend([
                "⚠️ **I need you to take action on this right away:**",
                f"   📞 Please call your doctor today or head to urgent care. I know that sounds scary, but these numbers tell me your lungs need attention sooner rather than later.",
                f"   ⏰ Don't put this off thinking it might get better on its own. Early treatment makes such a difference with lung conditions.",
                f"   📝 When you go, bring these test results with you. Your doctor will want to see exactly what we found."
            ])

        # Add smoking-specific clinical recommendations
        if "current" in patient.smoking_status:
            self.recommendations.extend([
                "",
                "🚭 **Smoking Cessation Protocol:**",
                f"   💔 **Primary Intervention**: Tobacco cessation represents the most significant modifiable factor for improving your respiratory function and preventing further decline.",
                f"   🆘 **Cessation Support**: Consider nicotine replacement therapy (patches, gum, lozenges), prescription medications (varenicline, bupropion), or behavioral counseling programs.",
                f"   ⏱️ **Recovery Timeline**: Pulmonary function improvement begins within weeks of cessation, with continued benefits over months to years regardless of smoking duration.",
                f"   🤝 **Resources**: Consult your healthcare provider for personalized cessation strategies, prescription aids, and referral to smoking cessation programs."
            ])
        elif "former" in patient.smoking_status:
            self.recommendations.extend([
                "",
                "🎊 **Smoking Cessation Success:**",
                f"   👏 **Excellent Decision**: Your smoking cessation has significantly contributed to your current respiratory health status and reduced future risk of pulmonary disease.",
                f"   🛡️ **Continued Protection**: Maintain avoidance of secondhand smoke exposure and environmental tobacco smoke to preserve respiratory gains.",
                f"   💪 **Long-term Benefits**: Continue to experience ongoing respiratory recovery and reduced risk of COPD, lung cancer, and other smoking-related conditions."
            ])

        # Add consultation recommendations with encouragement
        if risk_level == "High":
            self.recommendations.extend([
                "",
                "🩺 **Getting specialized care:**",
                f"   🫁 I really think you'd benefit from seeing a lung specialist (pulmonologist). They have tools and treatments that can help you feel so much better.",
                f"   🔬 They'll probably want to do some more detailed testing, but don't worry - it's all to figure out the best way to help you breathe easier.",
                f"   🌟 I've seen people with similar test results feel dramatically better with the right treatment plan. There's real hope here."
            ])

        # Add professional closing
        if risk_level != "High":
            self.recommendations.extend([
                "",
                "🤗 **Clinical Summary:** Your proactive approach to respiratory health monitoring is commendable. Implement these evidence-based recommendations gradually, prioritizing those most relevant to your current health status. Consistent adherence to preventive measures will optimize your long-term pulmonary function and overall respiratory health outcomes."
            ])

    def analyze(self, data_json: str) -> AnalysisResult:
        """Main analysis method that orchestrates all analysis components."""
        try:
            data = json.loads(data_json)
            user_id = data.get('user_id', 'there')  # Default to 'there' if no user_id
            patient = self._parse_patient_data(data)

            # Reset analysis state
            self.analysis_results = []
            self.current_medical_data = []
            self.additional_lung_measurements = []
            self.risk_factors = []
            self.recommendations = []
            self.respiratory_conditions = []

            # Display patient information and measurements
            self._display_patient_info(patient)
            self._display_measurements(patient)

            # Perform detailed analysis
            self._analyze_fev1(patient)
            self._analyze_fev2(patient)
            self._analyze_fev3(patient)
            self._analyze_fvc(patient)
            self._analyze_pef(patient)
            self._add_demographic_insights(patient)
            self._analyze_additional_parameters(patient)

            # Determine risk and identify conditions
            risk_level = self._determine_risk_level()
            self._identify_respiratory_conditions(patient)
            self._generate_recommendations(patient, risk_level)

            # Generate doctor summary and confidence assessment
            missing_params = self._get_missing_parameters(patient)
            confidence = self._assess_confidence(patient, missing_params)
            doctor_summary = self._generate_doctor_summary(patient, risk_level, confidence, user_id)

            return AnalysisResult(
                doctor_summary=doctor_summary,
                current_medical_data=self.current_medical_data,
                analysis=self.analysis_results,
                additional_lung_measurements=self.additional_lung_measurements,
                risk_level=risk_level,
                conditions=self.respiratory_conditions if self.respiratory_conditions else ["No specific respiratory conditions identified"],
                recommendations=self.recommendations,
                confidence=confidence,
                device_prompt="\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your lung capacity results? (Yes/No)**"
            )

        except ValueError as e:
            # Re-raise ValueError for required field validation
            if "required field" in str(e):
                raise e
            else:
                logging.error(f"Error analyzing lung capacity: {str(e)}")
                return AnalysisResult(
                    doctor_summary="Analysis could not be completed due to data validation error.",
                    current_medical_data=[],
                    analysis=[f"⚠️ Validation error: {str(e)}"],
                    additional_lung_measurements=[],
                    risk_level="Unknown",
                    conditions=["Validation failed"],
                    recommendations=["⚠️ Please check your input data and try again."],
                    confidence="Low",
                    device_prompt=""
                )
        except Exception as e:
            logging.error(f"Error analyzing lung capacity: {str(e)}")
            return AnalysisResult(
                doctor_summary="Analysis could not be completed due to data processing error.",
                current_medical_data=[],
                analysis=[f"⚠️ Analysis error: {str(e)}"],
                additional_lung_measurements=[],
                risk_level="Unknown",
                conditions=["Analysis failed"],
                recommendations=["⚠️ Unable to process spirometry data. Please consult a healthcare professional."],
                confidence="Low",
                device_prompt=""
            )


def analyze_lung_capacity(data_json: str) -> str:
    """
    Analyzes spirometry data to assess lung capacity and respiratory health.
    Uses professional analysis patterns to identify potential respiratory issues.

    Args:
        data_json: JSON string containing spirometry data and patient information

    Returns:
        JSON string with analysis results, risk assessment, and recommendations
    """
    try:
        # Handle potential encoding issues in the JSON string
        if isinstance(data_json, bytes):
            data_json = data_json.decode('utf-8')

        # Fix common encoding issues with special characters
        data_json = data_json.replace('\\xc2\\xb2', '²')  # Fix UTF-8 encoding of ²
        data_json = data_json.replace('kg/m\\xc2\\xb2', 'kg/m²')  # Specific BMI fix

    except Exception as e:
        logging.warning(f"Encoding issue in input data: {e}")

    analyzer = LungCapacityAnalyzer()
    result = analyzer.analyze(data_json)

    # Create ordered dictionary with the requested structure
    output_dict = {}
    output_dict["doctor_summary"] = result.doctor_summary
    output_dict["current_medical_data"] = result.current_medical_data
    output_dict["analysis"] = result.analysis
    output_dict["additional_lung_measurements"] = result.additional_lung_measurements
    output_dict["confidence_level"] = result.confidence
    output_dict["recommendations"] = result.recommendations
    output_dict["respiratory_risk_level"] = result.risk_level
    output_dict["device_recommendation_prompt"] = result.device_prompt
    output_dict["test_type"] = "lung_capacity"

    return json.dumps(output_dict, indent=4)


# Create the tool
lung_capacity_analyzer_tool = Tool(
    name="LungCapacityAnalyzer",
    func=analyze_lung_capacity,
    description="Analyzes spirometry data to assess lung capacity, identify respiratory risks like COPD/asthma, and provide respiratory health recommendations."
)
