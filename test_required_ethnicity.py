#!/usr/bin/env python3
"""Test required ethnicity field"""

import sys
import json

# Add current directory to path
sys.path.append('.')

from tools.tools_lung_capacity import analyze_lung_capacity

def test_required_ethnicity():
    """Test that ethnicity is now required"""
    
    test_cases = [
        {
            "name": "Missing ethnicity (should fail)",
            "payload": {
                "user_id": "Alex",
                "data": {
                    "Age": 30,
                    "Sex": "Male",
                    "FEV1": "3.2 L"
                }
            },
            "should_fail": True
        },
        {
            "name": "Empty ethnicity (should fail)",
            "payload": {
                "user_id": "<PERSON>",
                "data": {
                    "Age": 35,
                    "Sex": "Female",
                    "Ethnicity": "",
                    "FEV1": "2.8 L"
                }
            },
            "should_fail": True
        },
        {
            "name": "Valid ethnicity (should work)",
            "payload": {
                "user_id": "Maria",
                "data": {
                    "Age": 42,
                    "Sex": "Female",
                    "BMI": "26.5 kg/m²",
                    "Ethnicity": "Hispanic",
                    "Smoking_Status": "Non-Smoker, non-drinker",
                    "FEV1": "2.4 L",
                    "FEV2": "2.8 L",
                    "FEV3": "3.1 L"
                }
            },
            "should_fail": False
        },
        {
            "name": "Ethnicity with different case (should work)",
            "payload": {
                "user_id": "John",
                "data": {
                    "Age": 45,
                    "Sex": "Male",
                    "ethnicity": "asian",
                    "FEV1": "2.5 L"
                }
            },
            "should_fail": False
        },
        {
            "name": "Race field instead of Ethnicity (should work)",
            "payload": {
                "user_id": "David",
                "data": {
                    "Age": 38,
                    "Sex": "Male",
                    "Race": "African American",
                    "FEV1": "2.9 L"
                }
            },
            "should_fail": False
        }
    ]
    
    print("🧪 TESTING REQUIRED ETHNICITY FIELD")
    print("=" * 60)
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        
        try:
            result = analyze_lung_capacity(json.dumps(test_case['payload']))
            parsed_result = json.loads(result)
            
            if test_case['should_fail']:
                print("  ❌ UNEXPECTED SUCCESS: Should have failed but didn't")
            else:
                print("  ✅ SUCCESS: Analysis completed")
                
                # Check ethnicity in medical data
                medical_data_text = ' '.join(parsed_result['current_medical_data'])
                if 'Ethnicity:' in medical_data_text:
                    ethnicity_line = [line for line in parsed_result['current_medical_data'] if 'Ethnicity:' in line][0]
                    print(f"    📊 {ethnicity_line}")
                
                # Check ethnicity analysis
                analysis_text = ' '.join(parsed_result['analysis'])
                if 'ethnicity' in analysis_text.lower():
                    print("    🔬 Ethnicity-specific analysis included")
                
        except ValueError as e:
            if test_case['should_fail']:
                print(f"  ✅ EXPECTED FAILURE: {str(e)}")
            else:
                print(f"  ❌ UNEXPECTED FAILURE: {str(e)}")
        except Exception as e:
            print(f"  ❌ UNEXPECTED ERROR: {str(e)}")
    
    print(f"\n🎯 SUMMARY:")
    print(f"  ✅ Ethnicity is now a required field")
    print(f"  ✅ System validates ethnicity presence")
    print(f"  ✅ Accepts 'Ethnicity', 'ethnicity', or 'Race' fields")
    print(f"  ✅ Provides clear error message when missing")

if __name__ == "__main__":
    test_required_ethnicity()
